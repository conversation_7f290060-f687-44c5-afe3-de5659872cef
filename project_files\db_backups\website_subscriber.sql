-- --------------------------------------------------------
-- Host:                         127.0.0.1
-- Server version:               8.0.30 - MySQL Community Server - GPL
-- Server OS:                    Win64
-- HeidiSQL Version:             12.1.0.6537
-- --------------------------------------------------------
-- Dumping structure for table website_subscriber.posts
CREATE TABLE IF NOT EXISTS `posts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `website_id` bigint unsigned NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  <PERSON><PERSON><PERSON> `posts_website_id_foreign` (`website_id`),
  CONSTRAINT `posts_website_id_foreign` FOREIGN KEY (`website_id`) REFERENCES `websites` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table website_subscriber.posts: ~2 rows (approximately)
INSERT INTO `posts` (`id`, `website_id`, `title`, `description`, `created_at`, `updated_at`) VALUES
	(1, 1, 'demo post', 'demo', '2023-06-26 02:58:22', '2023-06-26 02:58:22'),
	(2, 1, 'demo post', 'demo', '2023-06-26 03:00:05', '2023-06-26 03:00:05');

-- Dumping structure for table website_subscriber.subscribers
CREATE TABLE IF NOT EXISTS `subscribers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `website_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `website_email_unique` (`website_id`,`email`),
  CONSTRAINT `subscribers_website_id_foreign` FOREIGN KEY (`website_id`) REFERENCES `websites` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table website_subscriber.subscribers: ~100 rows (approximately)
INSERT INTO `subscribers` (`id`, `website_id`, `name`, `email`, `created_at`, `updated_at`) VALUES
	(1, 3, 'Houston Williamson', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(2, 1, 'Dr. Buford Fahey', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(3, 2, 'Dr. Vena Nader II', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(4, 1, 'Miss Jaida Dietrich', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(5, 3, 'Arno Koss', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(6, 4, 'Ron Gutmann', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(7, 5, 'Layla VonRueden', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(8, 5, 'Prof. Curtis McGlynn DVM', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(9, 5, 'Fatima Kuphal', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(10, 5, 'Yazmin Beer', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(11, 2, 'Caroline Johns', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(12, 5, 'Ben Simonis', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(13, 4, 'Sandy Hodkiewicz', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(14, 3, 'Estella Kilback', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(15, 1, 'Ms. Marianne Kuhic', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(16, 5, 'Mr. Solon Bins', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(17, 5, 'Layne Collins PhD', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(18, 1, 'Pansy Bartoletti', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(19, 3, 'Myles Schroeder', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(20, 5, 'Kyle Gislason', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(21, 3, 'Mrs. Casandra Rogahn', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(22, 5, 'Jaren Nikolaus', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(23, 1, 'Zora Schroeder', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(24, 2, 'Rosalee Gaylord', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(25, 3, 'Mr. Einar Hoeger II', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(26, 3, 'Susanna Huel', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(27, 2, 'Mr. Omer Keeling DDS', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(28, 5, 'Prof. Jessy Dach III', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(29, 3, 'Viola Windler', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(30, 2, 'Lazaro Lang', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(31, 2, 'Mr. Ralph Harris DDS', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(32, 1, 'Marquise Wintheiser', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(33, 2, 'Jackeline Stiedemann II', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(34, 4, 'Prof. Clay Kuhn', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(35, 3, 'Osborne Bechtelar', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(36, 4, 'Marilie Rosenbaum', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(37, 1, 'Dovie Bogan', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(38, 1, 'Watson Lehner', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(39, 1, 'Laney West', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(40, 5, 'Gudrun Shields', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(41, 1, 'Berniece Rosenbaum IV', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(42, 3, 'Turner Mayert', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(43, 2, 'Kristoffer Bartoletti', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(44, 3, 'Miss Maybell Barrows', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(45, 5, 'Gustave Kessler PhD', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(46, 3, 'Major Oberbrunner', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(47, 5, 'Cordie Zulauf', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(48, 5, 'Garry Weissnat PhD', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(49, 2, 'Warren Tremblay', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(50, 1, 'Diamond Runolfsson', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(51, 4, 'Danyka Dietrich', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(52, 5, 'Barrett Casper', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(53, 1, 'Vickie Wolff', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(54, 4, 'Dortha Schulist', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(55, 5, 'Mrs. Myra Ledner', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(56, 4, 'Melba Harris', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(57, 1, 'Jimmy Brekke', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(58, 2, 'Dr. Tyra Cruickshank IV', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(59, 2, 'Dillan Mohr', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(60, 1, 'Prof. Antonio Nader III', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(61, 3, 'Pierce Terry', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(62, 1, 'Miss Mercedes Kertzmann', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(63, 2, 'Dr. Maud Kerluke II', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(64, 3, 'Carolina Veum', '<EMAIL>', '2023-06-26 02:55:43', '2023-06-26 02:55:43'),
	(65, 2, 'Serena Johnston II', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(66, 5, 'Zakary Bogisich', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(67, 2, 'Mrs. Rosalia Keebler I', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(68, 1, 'Prof. Eliseo Kuhic', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(69, 5, 'Ayden Kovacek', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(70, 4, 'Hipolito Halvorson', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(71, 3, 'Miss Aurore Auer Jr.', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(72, 1, 'Margarita Grady', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(73, 4, 'Allan Kulas', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(74, 2, 'Mustafa Schultz', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(75, 1, 'Prof. Micheal Strosin Sr.', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(76, 4, 'Prof. Greg Torphy', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(77, 2, 'Naomi Bradtke', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(78, 3, 'Prof. Paolo Weissnat III', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(79, 5, 'Ubaldo Swift', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(80, 4, 'Maegan Volkman', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(81, 5, 'Barry Bosco', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(82, 5, 'Ludie Homenick', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(83, 1, 'Prof. Carlotta Borer Sr.', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(84, 5, 'Miss Anabel Rempel V', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(85, 1, 'Stella Torphy', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(86, 4, 'Mitchel Boyer MD', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(87, 3, 'Toni Ebert', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(88, 4, 'Jerel Hickle', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(89, 1, 'Odessa Upton PhD', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(90, 3, 'Gloria Cremin', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(91, 5, 'Mr. Floy Weber III', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(92, 2, 'Dr. Schuyler Gislason V', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(93, 4, 'Felipe Hansen', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(94, 1, 'Hortense Shanahan', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(95, 4, 'Demario Dibbert DDS', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(96, 3, 'Estrella Glover', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(97, 5, 'Simone Reinger', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(98, 1, 'Lavinia Champlin', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(99, 5, 'Dianna Fay', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44'),
	(100, 3, 'Mrs. Karianne Casper', '<EMAIL>', '2023-06-26 02:55:44', '2023-06-26 02:55:44');

-- Dumping structure for table website_subscriber.websites
CREATE TABLE IF NOT EXISTS `websites` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table website_subscriber.websites: ~5 rows (approximately)
INSERT INTO `websites` (`id`, `name`, `url`, `created_at`, `updated_at`) VALUES
	(1, 'Cummerata, Fahey and Flatley', 'https://www.maggio.biz/quia-quae-qui-at', '2023-06-26 02:55:42', '2023-06-26 02:55:42'),
	(2, 'Kiehn, Mosciski and Buckridge', 'http://www.herzog.com/eum-a-qui-dignissimos-qui.html', '2023-06-26 02:55:42', '2023-06-26 02:55:42'),
	(3, 'Hahn, Buckridge and Schmeler', 'http://russel.com/et-voluptatem-possimus-esse', '2023-06-26 02:55:42', '2023-06-26 02:55:42'),
	(4, 'Langosh PLC', 'http://stokes.com/molestias-et-possimus-id-omnis', '2023-06-26 02:55:42', '2023-06-26 02:55:42'),
	(5, 'Mosciski, Crona and Rohan', 'http://www.considine.com/quam-dolorum-nulla-veniam-distinctio', '2023-06-26 02:55:42', '2023-06-26 02:55:42');
