<?php

use Illuminate\Database\Capsule\Manager as DB;
use Illuminate\Database\Schema\Blueprint;

class CreatePostsTable
{
    public function up()
    {
        DB::schema()->create('posts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('website_id');
            $table->string('title');
            $table->text('description');
            $table->timestamps();

            // Add foreign key constraint
            $table->foreign('website_id')->references('id')->on('websites');
        });
    }

    public function down()
    {
        DB::schema()->dropIfExists('posts');
    }
}
