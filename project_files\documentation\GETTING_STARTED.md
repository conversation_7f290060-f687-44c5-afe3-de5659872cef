# Getting Started

## Quick Start

1. Clone the repository:
   ```
   git clone https://github.com/nikhil-lu210/OOP-Structured-RAW-PHP-Starter-KIT.git
   cd OOP-Structured-RAW-PHP-Starter-KIT
   ```

2. Install dependencies:
   ```
   composer install
   ```

3. Set up environment:
   ```
   cp .env.example .env
   ```
   Edit the `.env` file with your database credentials.

4. Start the development server:
   ```
   php nikhil serve
   ```
   Or use the traditional method:
   ```
   php -S localhost:8000
   ```

5. Run migrations:
   ```
   php nikhil migrate
   ```

## Creating a New Model

Use the command line tool to create a model:
```
php nikhil make:model Post
```

This will create:
- `app/Models/Post.php` (Laravel-style model)

To create a model with migration:
```
php nikhil make:model Post -m
```

This will create:
- `app/Models/Post.php` (Laravel-style model)
- `database/migrations/YYYY_MM_DD_HHMMSS_create_posts_table.php` (Laravel-style migration)

## Creating Migrations

Create a new migration:
```
php nikhil make:migration create_posts_table
```

Run pending migrations:
```
php nikhil migrate
```

Check migration status:
```
php nikhil migrate:status
```

Rollback last migration:
```
php nikhil migrate:rollback
```

Fresh migration (drop all tables and re-run):
```
php nikhil migrate:fresh
```

## Using Alerts

```php
use App\Services\Alert;

// In your controller
Alert::success('Operation completed successfully');
Alert::error('Something went wrong');
Alert::info('Here is some information');
Alert::warning('Be careful with this action');

// Then redirect
return redirect('home.index');
```