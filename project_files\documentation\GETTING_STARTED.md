# Getting Started

## Quick Start

1. Clone the repository:
   ```
   git clone https://github.com/nikhil-lu210/OOP-Structured-RAW-PHP-Starter-KIT.git
   cd OOP-Structured-RAW-PHP-Starter-KIT
   ```

2. Install dependencies:
   ```
   composer install
   ```

3. Set up environment:
   ```
   cp .env.example .env
   ```
   Edit the `.env` file with your database credentials.

4. Start the development server:
   ```
   php command serve
   ```
   Or use the traditional method:
   ```
   php -S localhost:8000
   ```

5. Run migrations:
   ```
   php command migrate
   ```

## Creating a New Model

Use the command line tool:
```
php command make:model Post
```

This will create:
- `app/Models/Post/Post.php`
- `app/Models/Post/Traits/PostRelations.php`
- `app/Migrations/PostTable.php`

Then add your model to the migrations controller:
```php
// In app/Controllers/Migrations/MigrationController.php
public function create() {
    User::createTable();
    Post::createTable();
    // Add your new model here
    
    dd('Migration Created');
}
```

## Using Alerts

```php
use App\Services\Alert;

// In your controller
Alert::success('Operation completed successfully');
Alert::error('Something went wrong');
Alert::info('Here is some information');
Alert::warning('Be careful with this action');

// Then redirect
return redirect('home.index');
```