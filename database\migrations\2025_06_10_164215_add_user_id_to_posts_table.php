<?php

use Illuminate\Database\Capsule\Manager as DB;
use Illuminate\Database\Schema\Blueprint;

class AddUserIdToPostsTable
{
    public function up()
    {
        DB::schema()->table('posts', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->nullable()->after('website_id');
            $table->foreign('user_id')->references('id')->on('users');
        });
    }

    public function down()
    {
        DB::schema()->table('posts', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropColumn('user_id');
        });
    }
}
