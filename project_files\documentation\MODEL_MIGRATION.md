# Model & Migrations (Laravel-Style)
This project now uses Laravel-style model and migration structure with **illuminate/database** package. The structure follows Laravel conventions with proper command-line tools for generating models and migrations.

## Creating Models

### Method 1: Create Model Only
```bash
php nikhil make:model User
```

This creates `app/Models/User.php`:
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class User extends Model
{
    protected $table = 'users';

    protected $fillable = [
        // Add your fillable fields here
    ];

    // Add your relationships and methods here
}
```

### Method 2: Create Model with Migration
```bash
php nikhil make:model User -m
```

This creates both:
- `app/Models/User.php` (model file)
- `database/migrations/YYYY_MM_DD_HHMMSS_create_users_table.php` (migration file)

## Creating Migrations

### Create Migration Only
```bash
php nikhil make:migration create_users_table
```

This creates `database/migrations/YYYY_MM_DD_HHMMSS_create_users_table.php`:
```php
<?php

use Illuminate\Database\Capsule\Manager as DB;
use Illuminate\Database\Schema\Blueprint;

class CreateUsersTable
{
    public function up()
    {
        DB::schema()->create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->string('password');
            $table->timestamps();
        });
    }

    public function down()
    {
        DB::schema()->dropIfExists('users');
    }
}
```

### Adding Relationships to Models
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class User extends Model
{
    protected $table = 'users';

    protected $fillable = [
        'name',
        'email',
        'password'
    ];

    /**
     * Get the posts for the user.
     */
    public function posts()
    {
        return $this->hasMany(Post::class);
    }
}
```

## Running Migrations

### Run All Pending Migrations
```bash
php nikhil migrate
```

### Check Migration Status
```bash
php nikhil migrate:status
```

### Rollback Last Migration
```bash
php nikhil migrate:rollback
```

### Fresh Migration (Drop all tables and re-run)
```bash
php nikhil migrate:fresh
```

### Migration Commands Summary
- `php nikhil make:migration name` - Create a new migration
- `php nikhil migrate` - Run pending migrations
- `php nikhil migrate:status` - Show migration status
- `php nikhil migrate:rollback` - Rollback last migration
- `php nikhil migrate:fresh` - Drop all tables and re-run migrations

### Model Commands Summary
- `php nikhil make:model ModelName` - Create a new model
- `php nikhil make:model ModelName -m` - Create a new model with migration

## Example: Creating a Complete Feature

1. **Create model with migration:**
   ```bash
   php nikhil make:model Post -m
   ```

2. **Edit the migration file** in `database/migrations/` to add your table structure:
   ```php
   public function up()
   {
       DB::schema()->create('posts', function (Blueprint $table) {
           $table->id();
           $table->string('title');
           $table->text('content');
           $table->unsignedBigInteger('user_id');
           $table->timestamps();

           $table->foreign('user_id')->references('id')->on('users');
       });
   }
   ```

3. **Update your model** in `app/Models/Post.php`:
   ```php
   protected $fillable = [
       'title',
       'content',
       'user_id'
   ];

   public function user()
   {
       return $this->belongsTo(User::class);
   }
   ```

4. **Run the migration:**
   ```bash
   php nikhil migrate
   ```
