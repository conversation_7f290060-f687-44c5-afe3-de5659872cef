<?php
namespace App\Models\Website\Traits;

use App\Models\Post\Post;
use App\Models\Subscriber\Subscriber;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait WebsiteRelations
{

    public function subscribers(): Has<PERSON><PERSON> 
    {
        return $this->hasMany(Subscriber::class);
    }

    public function posts(): Has<PERSON><PERSON> 
    {
        return $this->hasMany(Post::class);
    }
}
