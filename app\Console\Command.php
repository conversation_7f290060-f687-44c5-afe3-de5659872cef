<?php
namespace App\Console;

use Exception;

class Command {
    public static function run($args) {
        if (empty($args) || !isset($args[1]) || $args[1] === 'help') {
            self::showHelp();
            return;
        }

        $fullCommand = $args[1];

        switch($fullCommand) {
            case 'serve':
                echo "Starting server at http://localhost:8000\n";
                shell_exec('php -S localhost:8000');
                break;
            case 'migrate':
                require_once(__DIR__ . '/../../bootstrap/init.php');
                self::handleMigrate(null);
                break;
            case 'migrate:rollback':
                require_once(__DIR__ . '/../../bootstrap/init.php');
                self::handleMigrate('rollback');
                break;
            case 'migrate:status':
                require_once(__DIR__ . '/../../bootstrap/init.php');
                self::handleMigrate('status');
                break;
            case 'migrate:fresh':
                require_once(__DIR__ . '/../../bootstrap/init.php');
                self::handleMigrate('fresh');
                break;
            case 'make:model':
                if (empty($args[2])) {
                    echo "Model name required\n";
                    return;
                }
                try {
                    $withMigration = in_array('-m', $args) || in_array('--migration', $args);
                    self::makeModel($args[2], $withMigration);
                } catch (Exception $e) {
                    echo "Error creating model: " . $e->getMessage() . "\n";
                }
                break;
            case 'make:migration':
                if (empty($args[2])) {
                    echo "Migration name required\n";
                    return;
                }
                try {
                    self::makeMigration($args[2]);
                } catch (Exception $e) {
                    echo "Error creating migration: " . $e->getMessage() . "\n";
                }
                break;
            default:
                echo "Command not found\n";
                self::showHelp();
        }
    }

    private static function showHelp() {
        echo "Available commands:\n";
        echo "  php nikhil serve                    - Start development server\n";
        echo "  php nikhil migrate                  - Run pending migrations\n";
        echo "  php nikhil migrate:rollback         - Rollback the last migration\n";
        echo "  php nikhil migrate:status           - Show migration status\n";
        echo "  php nikhil migrate:fresh            - Drop all tables and re-run migrations\n";
        echo "  php nikhil make:model ModelName     - Create a new model\n";
        echo "  php nikhil make:model ModelName -m  - Create a new model with migration\n";
        echo "  php nikhil make:migration name      - Create a new migration\n";
    }

    private static function makeModel($name, $withMigration = false) {
        // Ensure models directory exists
        $modelsDir = __DIR__ . '/../../app/Models';
        if (!is_dir($modelsDir)) {
            mkdir($modelsDir, 0755, true);
        }

        // Create model file directly in app/Models (Laravel style)
        $tableName = strtolower($name) . 's';
        $modelContent = "<?php\n\nnamespace App\\Models;\n\nuse Illuminate\\Database\\Eloquent\\Model;\n\nclass {$name} extends Model\n{\n    protected \$table = '{$tableName}';\n\n    protected \$fillable = [\n        // Add your fillable fields here\n    ];\n\n    // Add your relationships and methods here\n}";

        $modelPath = $modelsDir . '/' . $name . '.php';
        file_put_contents($modelPath, $modelContent);

        echo "Model {$name} created successfully at app/Models/{$name}.php\n";

        // Create migration if requested
        if ($withMigration) {
            $migrationName = 'create_' . $tableName . '_table';
            self::makeMigration($migrationName);
        }
    }

    private static function makeMigration($name) {
        // Ensure migrations directory exists
        $migrationsDir = __DIR__ . '/../../database/migrations';
        if (!is_dir($migrationsDir)) {
            mkdir($migrationsDir, 0755, true);
        }

        // Generate timestamp for migration filename
        $timestamp = date('Y_m_d_His');
        $filename = $timestamp . '_' . $name . '.php';
        $className = str_replace(' ', '', ucwords(str_replace('_', ' ', $name)));

        // Determine if this is a create table migration
        $isCreateTable = strpos($name, 'create_') === 0 && strpos($name, '_table') !== false;

        if ($isCreateTable) {
            // Extract table name from migration name
            $tableName = str_replace(['create_', '_table'], '', $name);
            $migrationContent = self::getCreateTableMigrationTemplate($className, $tableName);
        } else {
            $migrationContent = self::getGenericMigrationTemplate($className);
        }

        $migrationPath = $migrationsDir . '/' . $filename;

        $result = file_put_contents($migrationPath, $migrationContent);
        if ($result === false) {
            throw new Exception("Failed to write migration file");
        }

        echo "Migration {$filename} created successfully\n";
    }

    private static function handleMigrate($subCommand) {
        switch($subCommand) {
            case 'rollback':
                self::migrateRollback();
                break;
            case 'status':
                self::migrateStatus();
                break;
            case 'fresh':
                self::migrateFresh();
                break;
            default:
                self::migrate();
                break;
        }
    }

    private static function migrate() {
        // Create migrations table if it doesn't exist
        self::createMigrationsTable();

        $migrationsDir = __DIR__ . '/../../database/migrations';
        if (!is_dir($migrationsDir)) {
            echo "No migrations directory found\n";
            return;
        }

        $files = glob($migrationsDir . '/*.php');
        $executedMigrations = self::getExecutedMigrations();

        $pendingMigrations = [];
        foreach ($files as $file) {
            $filename = basename($file);
            if (!in_array($filename, $executedMigrations)) {
                $pendingMigrations[] = $file;
            }
        }

        if (empty($pendingMigrations)) {
            echo "Nothing to migrate\n";
            return;
        }

        foreach ($pendingMigrations as $migrationFile) {
            $filename = basename($migrationFile);
            echo "Migrating: {$filename}\n";

            require_once $migrationFile;

            // Extract class name from filename
            $className = self::getClassNameFromMigrationFile($filename);

            if (class_exists($className)) {
                $migration = new $className();
                $migration->up();
                self::recordMigration($filename);
                echo "Migrated: {$filename}\n";
            }
        }
    }

    private static function migrateRollback() {
        $executedMigrations = self::getExecutedMigrations();
        if (empty($executedMigrations)) {
            echo "Nothing to rollback\n";
            return;
        }

        $lastMigration = end($executedMigrations);
        echo "Rolling back: {$lastMigration}\n";

        $migrationsDir = __DIR__ . '/../../database/migrations';
        $migrationFile = $migrationsDir . '/' . $lastMigration;

        if (file_exists($migrationFile)) {
            require_once $migrationFile;
            $className = self::getClassNameFromMigrationFile($lastMigration);

            if (class_exists($className)) {
                $migration = new $className();
                $migration->down();
                self::removeMigrationRecord($lastMigration);
                echo "Rolled back: {$lastMigration}\n";
            }
        }
    }

    private static function migrateStatus() {
        $migrationsDir = __DIR__ . '/../../database/migrations';
        if (!is_dir($migrationsDir)) {
            echo "No migrations directory found\n";
            return;
        }

        $files = glob($migrationsDir . '/*.php');
        $executedMigrations = self::getExecutedMigrations();

        echo "Migration Status:\n";
        echo str_repeat('-', 50) . "\n";

        foreach ($files as $file) {
            $filename = basename($file);
            $status = in_array($filename, $executedMigrations) ? 'Ran' : 'Pending';
            echo sprintf("%-40s %s\n", $filename, $status);
        }
    }

    private static function migrateFresh() {
        echo "Dropping all tables...\n";
        // This is a simplified version - in a real implementation you'd want to be more careful
        $tables = \Illuminate\Database\Capsule\Manager::select('SHOW TABLES');
        foreach ($tables as $table) {
            $tableName = array_values((array)$table)[0];
            if ($tableName !== 'migrations') {
                \Illuminate\Database\Capsule\Manager::statement("DROP TABLE IF EXISTS `{$tableName}`");
            }
        }

        // Clear migration records
        \Illuminate\Database\Capsule\Manager::table('migrations')->truncate();

        echo "Running fresh migrations...\n";
        self::migrate();
    }

    private static function createMigrationsTable() {
        $schema = \Illuminate\Database\Capsule\Manager::schema();

        if (!$schema->hasTable('migrations')) {
            $schema->create('migrations', function ($table) {
                $table->increments('id');
                $table->string('migration');
                $table->integer('batch');
            });
        }
    }

    private static function getExecutedMigrations() {
        self::createMigrationsTable();
        return \Illuminate\Database\Capsule\Manager::table('migrations')
            ->orderBy('migration')
            ->pluck('migration')
            ->toArray();
    }

    private static function recordMigration($filename) {
        $batch = \Illuminate\Database\Capsule\Manager::table('migrations')->max('batch') + 1;
        \Illuminate\Database\Capsule\Manager::table('migrations')->insert([
            'migration' => $filename,
            'batch' => $batch
        ]);
    }

    private static function removeMigrationRecord($filename) {
        \Illuminate\Database\Capsule\Manager::table('migrations')
            ->where('migration', $filename)
            ->delete();
    }

    private static function getClassNameFromMigrationFile($filename) {
        // Extract class name from filename (remove timestamp and .php)
        $name = preg_replace('/^\d{4}_\d{2}_\d{2}_\d{6}_/', '', $filename);
        $name = str_replace('.php', '', $name);
        return str_replace(' ', '', ucwords(str_replace('_', ' ', $name)));
    }

    private static function getCreateTableMigrationTemplate($className, $tableName) {
        return "<?php

use Illuminate\\Database\\Capsule\\Manager as DB;
use Illuminate\\Database\\Schema\\Blueprint;

class {$className}
{
    public function up()
    {
        DB::schema()->create('{$tableName}', function (Blueprint \$table) {
            \$table->id();
            \$table->timestamps();
        });
    }

    public function down()
    {
        DB::schema()->dropIfExists('{$tableName}');
    }
}
";
    }

    private static function getGenericMigrationTemplate($className) {
        return "<?php

use Illuminate\\Database\\Capsule\\Manager as DB;
use Illuminate\\Database\\Schema\\Blueprint;

class {$className}
{
    public function up()
    {
        // Add your migration logic here
    }

    public function down()
    {
        // Add your rollback logic here
    }
}
";
    }
}