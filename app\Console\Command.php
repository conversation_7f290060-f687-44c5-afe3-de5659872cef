<?php
namespace App\Console;

class Command {
    public static function run($args) {
        if (empty($args) || !isset($args[1]) || $args[1] === 'help') {
            self::showHelp();
            return;
        }

        switch($args[1]) {
            case 'serve':
                echo "Starting server at http://localhost:8000\n";
                shell_exec('php -S localhost:8000');
                break;
            case 'migrate':
                require_once(__DIR__ . '/../../bootstrap/init.php');
                (new \App\Controllers\Migrations\MigrationController())->create();
                break;
            case 'make:model':
                if (empty($args[2])) {
                    echo "Model name required\n";
                    return;
                }
                self::makeModel($args[2]);
                break;
            default:
                echo "Command not found\n";
                self::showHelp();
        }
    }

    private static function showHelp() {
        echo "Available commands:\n";
        echo "  php nikhil serve - Start development server\n";
        echo "  php nikhil migrate - Run migrations\n";
        echo "  php nikhil make:model ModelName - Create model files\n";
    }

    private static function makeModel($name) {
        // Create model directory
        $dir = __DIR__ . '/../../app/Models/' . $name;
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        // Create traits directory
        $traitsDir = $dir . '/Traits';
        if (!is_dir($traitsDir)) {
            mkdir($traitsDir, 0755, true);
        }

        // Create model file
        $modelContent = "<?php\nnamespace App\\Models\\{$name};\n\nuse App\\migrations\\{$name}Table;\nuse Illuminate\\Database\\Eloquent\\Model;\nuse App\\Models\\{$name}\\Traits\\{$name}Relations;\n\nclass {$name} extends Model {\n\tuse {$name}Table, {$name}Relations;\n\n\tprotected \$table = '" . strtolower($name) . "s';\n}";
        file_put_contents($dir . '/' . $name . '.php', $modelContent);

        // Create relations trait
        $relationsContent = "<?php\nnamespace App\\Models\\{$name}\\Traits;\n\ntrait {$name}Relations\n{\n\t// Define your relationships here\n}";
        file_put_contents($traitsDir . '/' . $name . 'Relations.php', $relationsContent);

        // Create migration file
        $migrationDir = __DIR__ . '/../../app/Migrations';
        if (!is_dir($migrationDir)) {
            mkdir($migrationDir, 0755, true);
        }

        $migrationContent = "<?php\nnamespace App\\migrations;\n\nuse Illuminate\\Database\\Capsule\\Manager as DB;\nuse Illuminate\\Database\\Schema\\Blueprint;\n\ntrait {$name}Table\n{\n\tpublic static function createTable()\n\t{\n\t\t// Get the table name from the model\n\t\t\$table = (new static())->getTable();\n\n\t\t// Use the DB connection to create the table if it doesn't already exist\n\t\tif (!DB::connection()->getSchemaBuilder()->hasTable(\$table)) {\n\t\t\tDB::connection()->getSchemaBuilder()->create(\$table, function (Blueprint \$table) {\n\t\t\t\t\$table->bigIncrements('id');\n\t\t\t\t// Add your columns here\n\t\t\t\t\$table->timestamps();\n\t\t\t});\n\n\t\t\techo '<span style=\"color: green; font-weight: bold;\">' . ucfirst(\$table) . ' table created successfully.</span><br>';\n\t\t} else {\n\t\t\techo '<span style=\"color: red; font-weight: bold;\">' . ucfirst(\$table) . ' table already exists.</span><br>';\n\t\t}\n\t}\n\n\tpublic static function updateTable()\n\t{\n\t\t// Get the table name from the model\n\t\t\$table = (new static())->getTable();\n\n\t\t// Use the DB connection to modify the table\n\t\tDB::connection()->getSchemaBuilder()->table(\$table, function (Blueprint \$table) {\n\t\t\t// Define the modifications to be made to the table structure\n\t\t\t// For example, adding or modifying columns\n\t\t\t\$table->string('new_column')->nullable();\n\t\t});\n\n\t\techo ucfirst(\$table) . ' table updated successfully';\n\t}\n}";
        file_put_contents($migrationDir . '/' . $name . 'Table.php', $migrationContent);

        echo "Model {$name} created successfully\n";
    }
}