
# OOP Structured RAW PHP (Starter KIT)

  

## Cloning the project

    git clone https://github.com/nikhil-lu210/OOP-Structured-RAW-PHP-Starter-KIT.git

  

## Installation

  

1.  **Go to folder location:**  `cd /OOP-Structured-RAW-PHP-Starter-KIT`

2.  **Run command:**  `composer install`

3.  **Copy .env file:**  `cp .env.example .env`

4.  **Run in localhost using this command:**  `php nikhil serve`

    Or use the traditional method: `php -S localhost:8000`

  
  

## Docmentation Links

  

-  **[Model & Migrations](project_files/documentation/MODEL_MIGRATION.md)**
-  **[Sending Email](project_files/documentation/EMAILDOC.md)**
-  **[Displaying Alert](project_files/documentation/ALERT.md)**
