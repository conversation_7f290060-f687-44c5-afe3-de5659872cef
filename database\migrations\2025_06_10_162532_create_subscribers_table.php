<?php

use Illuminate\Database\Capsule\Manager as DB;
use Illuminate\Database\Schema\Blueprint;

class CreateSubscribersTable
{
    public function up()
    {
        DB::schema()->create('subscribers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('website_id');
            $table->string('name');
            $table->string('email');
            $table->timestamps();

            // Add foreign key constraint
            $table->foreign('website_id')->references('id')->on('websites');
        });
    }

    public function down()
    {
        DB::schema()->dropIfExists('subscribers');
    }
}
